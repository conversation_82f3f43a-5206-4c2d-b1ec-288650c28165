"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  FileText,
  <PERSON><PERSON>resh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>riangle,
  XCircle,
  Eye,
  Loader2,
} from "lucide-react";
import React, { useMemo, useState, useEffect } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";

import { Button } from "@/components/ui/Button";
import { Modal } from "@/components/ui/Modal";
import V2DataGrid from "@/components/ui/V2DataGrid";
import { APIService } from "@/service/api.service";
import { setToastAlert } from "@/slices/metaDataSlice";
import { ApiUtilities } from "@/utils/ApiUtilities";

interface ValidationProps {
  onNext?: () => void;
  ingestionData?: any;
  setTransformedData?: any;
}

interface ValidationSummary {
  total_files: number;
  valid_files: number;
  invalid_files: number;
  total_errors: number;
  total_warnings: number;
  all_files_valid: boolean;
}

interface ValidationFile {
  id: number;
  file_type: string;
  original_name: string;
  validation_status: "VALID" | "INVALID";
  validation_errors: any[];
  validation_warnings: any[];
  total_errors: number;
  total_warnings: number;
}

interface ValidationResponse {
  message: string;
  validation_summary: ValidationSummary;
  files: ValidationFile[];
}



const Validation = ({ onNext, ingestionData, setTransformedData }: ValidationProps) => {
  const router = useRouter();
  const dispatch = useDispatch();

  const apiService = useMemo(
    () => new APIService(dispatch, router),
    [dispatch, router]
  );

  const [validationData, setValidationData] = useState<ValidationResponse | null>(null);
  const [selectedFileIssues, setSelectedFileIssues] = useState<ValidationFile | null>(null);
  const [isIssuesModalOpen, setIsIssuesModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  // API Functions
  const advanceWorkflowStage = async (workflowRunId: string) => {
    const url = `${ApiUtilities.getApiServerUrlGenPro}/workflow-run/${workflowRunId}/next_stage/`;
    return await apiService.genproPostRequest(url, {});
  };

  // Utility functions
  const showSuccess = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: 'success', title: 'Success', content: message }));
  };

  const showError = (message: string, error?: any) => {
    const errorMessage = error?.response?.data?.message || error?.message || message;
    dispatch(setToastAlert({ isToastOpen: true, intent: 'error', title: 'Error', content: errorMessage }));
  };

  // Transform validation data for grid display with column names as keys
  const gridData = useMemo(() => {
    if (!validationData) return [];

    return validationData.files.map((file) => ({
      id: file.id,
      "FILE NAME": file.original_name,
      "FILE TYPE": file.file_type,
      "VALIDATION STATUS": file.validation_status === "VALID" ? (
        <span className="text-xs font-medium  text-green-700 ">
          Valid
        </span>
      ) : (
        <span className=" text-red-700 ">
          Invalid
        </span>
      ),
      ISSUES:
        file.total_errors > 0 || file.total_warnings > 0 ? (
          <button
            onClick={() => handleViewIssues(file)}
            className="flex items-center space-x-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 px-2 py-1 rounded transition-colors duration-200"
          >
            <Eye className="w-4 h-4" />
            <span className="text-sm">
              {file.total_errors > 0 && (
                <span className="text-red-600 font-medium">{file.total_errors} error{file.total_errors > 1 ? 's' : ''}</span>
              )}
              {file.total_errors > 0 && file.total_warnings > 0 && <span className="text-gray-400">, </span>}
              {file.total_warnings > 0 && (
                <span className="text-yellow-600 font-medium">{file.total_warnings} warning{file.total_warnings > 1 ? 's' : ''}</span>
              )}
            </span>
          </button>
        ) : (
          <span className="text-green-600 text-sm font-medium">No issues</span>
        ),
      hasIssues: file.total_errors > 0 || file.total_warnings > 0,
    }));
  }, [validationData]);

  // Handle viewing issues for a specific file
  const handleViewIssues = (file: ValidationFile) => {
    setSelectedFileIssues(file);
    setIsIssuesModalOpen(true);
  };

  // Handle closing the issues modal
  const handleCloseIssuesModal = () => {
    setIsIssuesModalOpen(false);
    setSelectedFileIssues(null);
  };

  const handleContinueToTransformation = async () => {
    setSaveLoading(true);

    try {
      const workflowRunId = (validationData as any)?.workflow_run?.id ||
                           (ingestionData as any)?.workflow_run?.id ||
                           (Array.isArray(ingestionData) ? null : (ingestionData as any)?.workflow_run?.id);

      if (!workflowRunId) {
        showError('Workflow run ID not found. Please restart the process.');
        return;
      }

      const response = await advanceWorkflowStage(workflowRunId);

      if (response.data) {
        setTransformedData(response.data);
      }

      showSuccess('Successfully moved to transformation stage.');
      onNext?.();

    } catch (error: any) {
      console.error('Failed to advance to next stage:', error);
      showError('Failed to proceed to transformation stage', error);
    } finally {
      setSaveLoading(false);
    }
  };

  // Transform issues data for modal display
  const issuesData = useMemo(() => {
    if (!selectedFileIssues) return [];

    const issues: any[] = [];

    // Add errors
    selectedFileIssues.validation_errors.forEach((error, index) => {
      if (typeof error === "string") {
        issues.push({
          id: `error-${index}`,
          TYPE: (
            <div className="flex items-center space-x-2">
              <XCircle className="w-4 h-4 text-red-500" />
              <span className="text-sm font-medium text-red-600">Error</span>
            </div>
          ),
          ISSUE: error,
          "ACTION REQUIRED": "",
          "RECORD DETAILS": "",
        });
      } else {
        issues.push({
          id: `error-${index}`,
          TYPE: (
            <div className="flex items-center space-x-2">
              <XCircle className="w-4 h-4 text-red-500" />
              <span className="text-sm font-medium text-red-600">Error</span>
            </div>
          ),
          ISSUE: error.issue || error,
          "ACTION REQUIRED": error.action_required || "",
          "RECORD DETAILS": error.record_details || "",
        });
      }
    });

    // Add warnings
    selectedFileIssues.validation_warnings.forEach((warning, index) => {
      if (typeof warning === "string") {
        issues.push({
          id: `warning-${index}`,
          TYPE: (
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-4 h-4 text-yellow-500" />
              <span className="text-sm font-medium text-yellow-600">Warning</span>
            </div>
          ),
          ISSUE: warning,
          "ACTION REQUIRED": "",
          "RECORD DETAILS": "",
        });
      } else {
        issues.push({
          id: `warning-${index}`,
          TYPE: (
            <div className="flex items-center space-x-2">
              <AlertTriangle className="w-4 h-4 text-yellow-500" />
              <span className="text-sm font-medium text-yellow-600">Warning</span>
            </div>
          ),
          ISSUE: warning.issue || warning,
          "ACTION REQUIRED": warning.action_suggested || "",
          "RECORD DETAILS": warning.record_details || "",
        });
      }
    });

    return issues;
  }, [selectedFileIssues]);

  // Issues modal header configuration
  const issuesHeaderList = [
    {
      key: "type",
      name: "TYPE",
      width: 100,
      filterType: ""
    },
    {
      key: "issue",
      name: "ISSUE",
      width: 400,
      filterType: ""
    },
    {
      key: "action_required",
      name: "ACTION REQUIRED",
      width: 250,
      filterType: ""
    },
    {
      key: "record_details",
      name: "RECORD DETAILS",
      width: 200,
      filterType: ""
    },
  ];

  // Grid column configuration
  const headerList = [
    {
      key: "file_name",
      name: "FILE NAME",
      width: 300,
      filterType: ""
    },
    {
      key: "file_type",
      name: "FILE TYPE",
      width: 120,
      filterType: ""
    },
    {
      key: "validation_status",
      name: "VALIDATION STATUS",
      width: 150,
      filterType: ""
    },
    {
      key: "issues",
      name: "ISSUES",
      // width: 50,
      filterType: ""
    },
  ];



  // Load validation data on component mount or when ingestionData changes
  useEffect(() => {
    if (ingestionData) {
      loadValidationData();
    }
  }, [ingestionData]);

  const loadValidationData = () => {
    try {
      // Check if we have validation data from the ingestion phase
      if (ingestionData) {
        // The API response structure has validation_summary and files at root level
        // Check if ingestionData is the direct API response
        if (!Array.isArray(ingestionData) && (ingestionData as any).validation_summary && (ingestionData as any).files) {
          // Direct API response structure - this is what we expect from the ingestion API
          setValidationData(ingestionData as ValidationResponse);
        } else if (Array.isArray(ingestionData) && ingestionData.length > 0) {
          // Fallback: if it's an array of files, create validation structure
          const validationResponse: ValidationResponse = {
            message: "Files uploaded successfully",
            validation_summary: {
              total_files: ingestionData.length,
              valid_files: ingestionData.filter((f: any) => f.validation_status === 'VALID').length,
              invalid_files: ingestionData.filter((f: any) => f.validation_status === 'INVALID').length,
              total_errors: ingestionData.reduce((sum: number, f: any) => sum + (f.total_errors || 0), 0),
              total_warnings: ingestionData.reduce((sum: number, f: any) => sum + (f.total_warnings || 0), 0),
              all_files_valid: ingestionData.every((f: any) => f.validation_status === 'VALID')
            },
            files: ingestionData
          };
          setValidationData(validationResponse);
        }
      }
    } catch (error: any) {
      console.error("Failed to load validation data:", error);
    }
  };

  return (
    <div className="h-full flex flex-col overflow-hidden p-4">

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-0 mb-3">
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden h-full flex flex-col">
          {!validationData ? (
            <div className="flex items-center justify-center h-full">
              <div className="flex items-center space-x-3">
                <RefreshCw className="w-6 h-6 animate-spin text-blue-600" />
                <span className="text-lg text-gray-600">Loading validation data...</span>
              </div>
            </div>
          ) : validationData.files?.length > 0 ? (
            <div className="flex flex-col h-full">
              {/* Summary Cards */}
              <div className="p-4 pb-0">
                <div className="grid grid-cols-5 gap-3">
                  {/* Total Files Card */}
                  <div className="bg-blue-50 rounded-lg border border-blue-200 p-3">
                    <div className="flex items-center space-x-2">
                      <FileText className="w-4 h-4 text-blue-600" />
                      <div>
                        <p className="text-xs font-medium text-blue-600">Total Files</p>
                        <p className="text-lg font-semibold text-blue-700">{validationData.validation_summary.total_files}</p>
                      </div>
                    </div>
                  </div>

                  {/* Valid Files Card */}
                  <div className="bg-green-50 rounded-lg border border-green-200 p-3">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <div>
                        <p className="text-xs font-medium text-green-600">Valid Files</p>
                        <p className="text-lg font-semibold text-green-700">{validationData.validation_summary.valid_files}</p>
                      </div>
                    </div>
                  </div>

                  {/* Invalid Files Card */}
                  <div className="bg-red-50 rounded-lg border border-red-200 p-3">
                    <div className="flex items-center space-x-2">
                      <XCircle className="w-4 h-4 text-red-600" />
                      <div>
                        <p className="text-xs font-medium text-red-600">Invalid Files</p>
                        <p className="text-lg font-semibold text-red-700">{validationData.validation_summary.invalid_files}</p>
                      </div>
                    </div>
                  </div>

                  {/* Total Errors Card */}
                  <div className="bg-orange-50 rounded-lg border border-orange-200 p-3">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="w-4 h-4 text-orange-600" />
                      <div>
                        <p className="text-xs font-medium text-orange-600">Total Errors</p>
                        <p className="text-lg font-semibold text-orange-700">{validationData.validation_summary.total_errors}</p>
                      </div>
                    </div>
                  </div>

                  {/* Total Warnings Card */}
                  <div className="bg-yellow-50 rounded-lg border border-yellow-200 p-3">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="w-4 h-4 text-yellow-600" />
                      <div>
                        <p className="text-xs font-medium text-yellow-600">Total Warnings</p>
                        <p className="text-lg font-semibold text-yellow-700">{validationData.validation_summary.total_warnings}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="w-[100%] overflow-hidden mt-4 mx-auto">
                <V2DataGrid
                  headerList={headerList}
                  data={gridData}
                />
              </div>
            </div>
          ) : (
            <div className="p-4">
              <div className="text-center text-gray-500 py-8">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p className="text-lg font-medium mb-2">No Validation Data</p>
                <p className="text-sm">Please upload files in the ingestion phase first</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Status Note */}
      {validationData && (
        <div className="shrink-0 mb-3">
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
            <div className="flex items-center space-x-2">
              {validationData.validation_summary.all_files_valid ? (
                <>
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-gray-800">All files passed validation and are ready for processing</span>
                </>
              ) : (
                <>
                  <AlertTriangle className="w-4 h-4 text-yellow-600" />
                  <span className="text-sm font-medium text-gray-800">Some files require attention before proceeding to the next stage</span>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Navigation Controls - Fixed Footer */}
      <div className="shrink-0 rounded border border-lightgray-100 bg-white-200 p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CheckCircle className="size-4 text-green-500" />
            <span className="text-xs font-medium text-green-600">Validation complete</span>
            <span className="text-xs text-gray-400">•</span>
            <span className="text-xs text-gray-600">Ready to proceed to next step</span>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Button
              onClick={handleContinueToTransformation}
              className="flex h-[32px] items-center bg-blue-600 px-4 text-xs font-semibold text-white-200"
              disabled={saveLoading}
            >
              {saveLoading ? (
                <>
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  Processing...
                </>
              ) : (
                'Continue to Transformation →'
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Issues Detail Modal */}
      <Modal
        isOpen={isIssuesModalOpen}
        closeModal={handleCloseIssuesModal}
        headerTitle={`Issues for: ${selectedFileIssues?.original_name || ''}`}
        component={
          <div className="p-4">
            {selectedFileIssues && (
              <div className="space-y-4">
                {/* Summary */}
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-semibold text-gray-800 flex items-center space-x-2">
                      <FileText className="w-5 h-5 text-gray-600" />
                      <span>Issue Summary</span>
                    </h4>
                    <div className="flex items-center space-x-4">
                      {selectedFileIssues.total_errors > 0 && (
                        <div className="flex items-center space-x-1 bg-red-50 px-2 py-1 rounded-full border border-red-200">
                          <XCircle className="w-4 h-4 text-red-500" />
                          <span className="text-sm font-medium text-red-600">
                            {selectedFileIssues.total_errors} Error{selectedFileIssues.total_errors > 1 ? 's' : ''}
                          </span>
                        </div>
                      )}
                      {selectedFileIssues.total_warnings > 0 && (
                        <div className="flex items-center space-x-1 bg-yellow-50 px-2 py-1 rounded-full border border-yellow-200">
                          <AlertTriangle className="w-4 h-4 text-yellow-500" />
                          <span className="text-sm font-medium text-yellow-600">
                            {selectedFileIssues.total_warnings} Warning{selectedFileIssues.total_warnings > 1 ? 's' : ''}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">File Type:</span>
                      <span className="ml-2 font-medium text-gray-800 bg-blue-50 px-2 py-1 rounded border border-blue-200">
                        {selectedFileIssues.file_type}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Status:</span>
                      <span className={`ml-2 font-medium px-2 py-1 rounded border ${
                        selectedFileIssues.validation_status === 'VALID'
                          ? 'text-green-700 bg-green-50 border-green-200'
                          : 'text-red-700 bg-red-50 border-red-200'
                      }`}>
                        {selectedFileIssues.validation_status}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Issues Table */}
                {issuesData.length > 0 ? (
                  <div className="border border-gray-200 rounded-lg overflow-hidden">
                    <V2DataGrid
                      data={issuesData}
                      headerList={issuesHeaderList}
                    />
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <p>No detailed issues available</p>
                  </div>
                )}
              </div>
            )}
          </div>
        }
        isActionButtonVisible={false}
        isCancelVisible={true}
        cancelbuttonText="Close"
        footerSecondaryEventHandler={handleCloseIssuesModal}
        panelWidth="w-[800px]"
      />
    </div>
  );
};

export default Validation;
