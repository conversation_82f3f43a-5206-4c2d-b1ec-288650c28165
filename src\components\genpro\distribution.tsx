'use client';

import React, { useState } from 'react';
import { CheckCircle, Download } from 'lucide-react';

import { Button } from '@/components/ui/Button';
import { Tabs } from '@/components/ui/Tabs';
import V2DataGrid from '@/components/ui/V2DataGrid';

interface DistributionProps {
  onNext?: () => void;
  uploadedFiles?: any;
  onDistributionComplete?: (data: any) => void;
  pivotData: any;
}

const Distribution: React.FC<DistributionProps> = ({
  onNext,
  pivotData
}) => {
  const [selectedTab, setSelectedTab] = useState(0);

  // Extract data from pivotData
  const distributionData = pivotData?.result?.database_records || {};
  const summaryData = distributionData.pivot_summaries || {};
  const splitsData = distributionData.pivot_splits || {};
  const distributionsData = distributionData.pivot_distributions || {};

  // SMC Headers for Summary
  const smcSummaryHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Total BF Amount',  isSortable: true, filterType: '' },
  ];

  // Vessel Headers for Summary
  const vesselSummaryHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Vessel Name',  isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Total BF Amount', width: 150, isSortable: true, filterType: '' },
  ];

  // SMC Headers for Splits
  const smcSplitsHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Entity Amount', width: 150, isSortable: true, filterType: '' },
    { name: 'Seachef Amount', width: 150, isSortable: true, filterType: '' },
  ];

  // Vessel Headers for Splits
  const vesselSplitsHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Vessel Name', width: 150, isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Entity Amount', width: 150, isSortable: true, filterType: '' },
    { name: 'Seachef Amount', width: 150, isSortable: true, filterType: '' },
  ];

  // SMC Headers for Distributions
  const smcDistributionHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Base BF Amount', width: 150, isSortable: true, filterType: '' },
    { name: 'Total BF Including 50%', width: 180, isSortable: true, filterType: '' },
    { name: 'BF Percentage', width: 150, isSortable: true, filterType: '' },
    { name: 'Final Distribution Amount', width: 200, isSortable: true, filterType: '' },
  ];

  // Vessel Headers for Distributions
  const vesselDistributionHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Vessel Name', width: 150, isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Base BF Amount', width: 150, isSortable: true, filterType: '' },
    { name: 'Total BF Including 50%', width: 180, isSortable: true, filterType: '' },
    { name: 'BF Percentage', width: 150, isSortable: true, filterType: '' },
    { name: 'Final Distribution Amount', width: 200, isSortable: true, filterType: '' },
  ];

  // Map SMC Summary data
  const smcSummaryData = (summaryData.smc?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'SMC Name': item.smc_name,
    'Total BF Amount': item.total_bf_amount?.toFixed(2),
  }));

  // Map Vessel Summary data
  const vesselSummaryData = (summaryData.vessel?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'Vessel Name': item.vessel_name,
    'SMC Name': item.smc_name,
    'Total BF Amount': item.total_bf_amount?.toFixed(2),
  }));

  // Map SMC Splits data
  const smcSplitsDataMapped = (splitsData.smc?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'SMC Name': item.smc_name,
    'Entity Amount': item.entity_amount?.toFixed(2),
    'Seachef Amount': item.seachef_amount?.toFixed(2),
  }));

  // Map Vessel Splits data
  const vesselSplitsDataMapped = (splitsData.vessel?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'Vessel Name': item.vessel_name,
    'SMC Name': item.smc_name,
    'Entity Amount': item.entity_amount?.toFixed(2),
    'Seachef Amount': item.seachef_amount?.toFixed(2),
  }));

  // Map SMC Distribution data
  const smcDistributionData = (distributionsData.smc?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'SMC Name': item.smc_name,
    'Base BF Amount': item.base_bf_amount?.toFixed(2),
    'Total BF Including 50%': item.total_bf_including_50_percent?.toFixed(2),
    'BF Percentage': item.bf_percentage?.toFixed(4) + '%',
    'Final Distribution Amount': item.final_distribution_amount?.toFixed(2),
  }));

  // Map Vessel Distribution data
  const vesselDistributionData = (distributionsData.vessel?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'Vessel Name': item.vessel_name,
    'SMC Name': item.smc_name,
    'Base BF Amount': item.base_bf_amount?.toFixed(2),
    'Total BF Including 50%': item.total_bf_including_50_percent?.toFixed(2),
    'BF Percentage': item.bf_percentage?.toFixed(4) + '%',
    'Final Distribution Amount': item.final_distribution_amount?.toFixed(2),
  }));

  // Summary data
  const smcRecords = pivotData?.result?.smc_summary_records || 0;
  const vesselRecords = pivotData?.result?.vessel_summary_records || 0;

  // Tab data configuration
  const tabData = [
    {
      title: 'Summary',
      component: (
        <div className="flex gap-6">
          <div className="w-[48%] ">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">SMC Summary</h3>
            <div>
              <V2DataGrid data={smcSummaryData} headerList={smcSummaryHeaders} />
            </div>
          </div>
          <div className="w-[48%]">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">Vessel Summary</h3>
            <div>
              <V2DataGrid data={vesselSummaryData} headerList={vesselSummaryHeaders} />
            </div>
          </div>
        </div>
      ),
      icon: '',
      iconPosition: 'FIRST' as const,
    },
    {
      title: 'Splits',
      component: (
        <div className="flex gap-6">
          <div className="w-[48%]">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">SMC Splits</h3>
            <div>
              <V2DataGrid data={smcSplitsDataMapped} headerList={smcSplitsHeaders} />
            </div>
          </div>
          <div className="w-[48%]">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">Vessel Splits</h3>
            <div>
              <V2DataGrid data={vesselSplitsDataMapped} headerList={vesselSplitsHeaders} />
            </div>
          </div>
        </div>
      ),
      icon: '',
      iconPosition: 'FIRST' as const,
    },
    {
      title: 'Distribution',
      component: (
        <div className="flex gap-6">
          <div className="w-[48%]">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">SMC Distribution</h3>
            <div>
              <V2DataGrid data={smcDistributionData} headerList={smcDistributionHeaders} />
            </div>
          </div>
          <div className="w-[48%]">
            <h3 className="text-lg font-semibold mb-3 text-gray-800">Vessel Distribution</h3>
            <div>
              <V2DataGrid data={vesselDistributionData} headerList={vesselDistributionHeaders} />
            </div>
          </div>
        </div>
      ),
      icon: '',
      iconPosition: 'FIRST' as const,
    },
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-0 mb-3">
        {/* Tabs with Download Button */}
        <div className="bg-white rounded-lg">
          <div className="flex items-center justify-between p-4 pb-0">
            <div className="flex-1">
              <Tabs
                data={tabData}
                selectedIndex={selectedTab}
                onChange={setSelectedTab}
              />
            </div>
            <div className="ml-4">
              <Button
                onClick={() => {
                  const reportUrl = pivotData?.result?.comprehensive_report_url;
                  if (reportUrl) {
                    window.open(reportUrl, '_blank');
                  }
                }}
                className="flex h-[28px] items-center px-3 text-xs"
                intent="secondary"
                disabled={!pivotData?.result?.comprehensive_report_url}
              >
                <Download className="w-3 h-3 mr-1" />
                Download Report
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Controls - Fixed Footer */}
      <div className="shrink-0 rounded border border-lightgray-100 bg-white-200 p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <CheckCircle className="size-4 text-green-500" />
            <span className="text-xs font-medium text-green-600">Distribution complete</span>
            <span className="text-xs text-gray-400">•</span>
            <span className="text-xs text-gray-600">{smcRecords} SMC records • {vesselRecords} vessel records processed</span>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Button
              onClick={() => onNext?.()}
              className="flex h-[32px] items-center bg-blue-600 px-4 text-xs font-semibold text-white-200"
            >
              Continue to Export →
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Distribution;