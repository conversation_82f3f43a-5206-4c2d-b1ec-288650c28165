'use client';

import React, { useState, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import {
  <PERSON><PERSON><PERSON>,
  BarChart3,
  DollarSign,
  Calculator,
  TrendingUp,
  Download,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';

import { Button } from '@/components/ui/Button';
import Table from '@/components/ui/Table';
import { APIService } from '@/service/api.service';
import { setToastAlert } from '@/slices/metaDataSlice';
import { ApiUtilities } from '@/utils/ApiUtilities';

interface DistributionProps {
  onNext?: () => void;
  uploadedFiles?: any;
  onDistributionComplete?: (data: any) => void;
  pivotData: any
}

const LoadingSpinner = ({ text }: { text: string }) => (
  <div className="p-4 flex items-center justify-center">
    <div className="flex items-center space-x-2">
      <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
      <span className="text-gray-600">{text}</span>
    </div>
  </div>
);

const Distribution: React.FC<DistributionProps> = ({ onNext, uploadedFiles, onDistributionComplete, pivotData }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), [dispatch, router]);

  const [selectedView, setSelectedView] = useState('summary');
  const [distributionResult, setDistributionResult] = useState<any>(null);
  const [pivotResults, setPivotResults] = useState<any>(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  // API Functions
  // const generatePivotTables = async (workflowId: string) => {
  //   const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproDistributionPivot?.url || '/distribution/pivot'}`;
  //   return await apiService.genproPostRequest(url, { workflow_id: workflowId });
  // };

  // const calculateDistribution = async (workflowId: string) => {
  //   const url = `${ApiUtilities.getApiServerUrlGenPro}${ApiUtilities.apiPath.genproDistributionCalculate?.url || '/distribution/calculate'}`;
  //   return await apiService.genproPostRequest(url, { workflow_id: workflowId });
  // };

  // Utility functions
  const showSuccess = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: 'success', title: 'Success', content: message }));
  };

  const showError = (message: string, error?: any) => {
    const errorMessage = error?.response?.data?.message || error?.message || message;
    dispatch(setToastAlert({ isToastOpen: true, intent: 'error', title: 'Error', content: errorMessage }));
  };

  const showInfo = (message: string) => {
    dispatch(setToastAlert({ isToastOpen: true, intent: 'info', title: 'Info', content: message }));
  };

  // Mock data for demonstration
  const distributionData = [
    {
      finalSMC: 'BSM HEL',
      finalVessel: 'Vessel X',
      totalBF: 33333,
      totalBFIncluding50: 35000,
      bfPercentage: 26.7,
      distributionAmount: 33375,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'BSM CYP',
      finalVessel: 'Vessel Y',
      totalBF: 50000,
      totalBFIncluding50: 52500,
      bfPercentage: 40.0,
      distributionAmount: 50000,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'SEACHEF',
      finalVessel: 'SEACHEF',
      totalBF: 16667,
      totalBFIncluding50: 18750,
      bfPercentage: 13.3,
      distributionAmount: 16625,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'BSM SG',
      finalVessel: 'Vessel C',
      totalBF: 15000,
      totalBFIncluding50: 15750,
      bfPercentage: 12.0,
      distributionAmount: 15000,
      excludeFromDistribution: false
    },
    {
      finalSMC: 'Frontline',
      finalVessel: 'Frontline',
      totalBF: 10000,
      totalBFIncluding50: 10000,
      bfPercentage: 0,
      distributionAmount: 0,
      excludeFromDistribution: true
    }
  ];

  const seachefSplitData = [
    {
      smc: 'BSM CHI',
      vessel: 'SEACHEF A',
      originalAmount: 5000,
      splitAmount: 2500,
      allocation: 'SMC'
    },
    {
      smc: 'SEACHEF',
      vessel: 'SEACHEF',
      originalAmount: 5000,
      splitAmount: 2500,
      allocation: 'SEACHEF'
    }
  ];

  const totalBFToDistribute = 125000;
  const totalDistributed = distributionData.reduce((sum, item) => sum + item.distributionAmount, 0);
  const distributionSummary = {
    totalBF: totalBFToDistribute,
    totalDistributed: totalDistributed,
    remaining: totalBFToDistribute - totalDistributed,
    entities: distributionData.length,
    excludedEntities: distributionData.filter(d => d.excludeFromDistribution).length
  };

  const handleGeneratePivotTables = async () => {
    setIsCalculating(true);

    try {
      const workflowId = (uploadedFiles as any)?.workflow_run?.id || '1';
      const response = await generatePivotTables(workflowId);
      setPivotResults(response.data);
      
      showSuccess(`Generated pivot tables with totals: $${response.data.pivot1_total_amount?.toLocaleString()} and $${response.data.pivot2_total_amount?.toLocaleString()}`);
    } catch (error: any) {
      console.error('Pivot generation failed:', error);
      showError('Failed to generate pivot tables', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleCalculateDistribution = async () => {
    setIsCalculating(true);

    try {
      const workflowId = (uploadedFiles as any)?.workflow_run?.id || '1';
      const response = await calculateDistribution(workflowId);
      setDistributionResult(response.data);
      
      showSuccess(`Distributed $${response.data.total_distributed?.toLocaleString()} across ${response.data.distribution_records} records`);
    } catch (error: any) {
      console.error('Distribution calculation failed:', error);
      showError('Failed to calculate distribution', error);
    } finally {
      setIsCalculating(false);
    }
  };

  const handleProceed = async () => {
    setSaveLoading(true);
    
    try {
      onDistributionComplete?.({
        ...uploadedFiles,
        distributionResult,
        pivotResults
      });
      showSuccess('Distribution completed successfully.');
      onNext?.();
    } catch (error: any) {
      console.error('Distribution completion failed:', error);
      showError('Failed to complete distribution', error);
    } finally {
      setSaveLoading(false);
    }
  };

  const handleExportResults = () => {
    showInfo('Distribution results will be available in the Export tab');
  };

  const summaryHeader = [
    { title: 'Final SMC', align: 'left' },
    { title: 'Final Vessel', align: 'left' },
    { title: 'Total BF', align: 'right' },
    { title: 'Total BF (Including 50%)', align: 'right' },
    { title: 'BF %', align: 'right' },
    { title: 'Distribution Amount', align: 'right' },
  ];

  const summaryData = distributionData.map((item, index) => ({
    id: index,
    components: [
      {
        type: 'text',
        value: (
          <div className="flex items-center space-x-2">
            <PieChart className="w-4 h-4 text-gray-500" />
            <span className="font-medium">{item.finalSMC}</span>
          </div>
        )
      },
      { type: 'text', value: item.finalVessel },
      {
        type: 'text',
        value: (
          <span className="font-mono text-right">
            ${item.totalBF.toLocaleString()}
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className="font-mono text-right">
            ${item.totalBFIncluding50.toLocaleString()}
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className={`font-semibold text-right ${
            item.excludeFromDistribution ? 'text-gray-400' : 'text-blue-600'
          }`}>
            {item.bfPercentage.toFixed(1)}%
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className={`font-mono font-semibold text-right ${
            item.excludeFromDistribution ? 'text-gray-400' : 'text-green-600'
          }`}>
            ${item.distributionAmount.toLocaleString()}
            {item.excludeFromDistribution && ' (Excluded)'}
          </span>
        )
      },
    ],
  }));

  const seachefHeader = [
    { title: 'SMC', align: 'left' },
    { title: 'Vessel', align: 'left' },
    { title: 'Original Amount', align: 'right' },
    { title: '50% Split Amount', align: 'right' },
    { title: 'Allocation Type', align: 'left' },
  ];

  const seachefData = seachefSplitData.map((item, index) => ({
    id: index,
    components: [
      { type: 'text', value: item.smc },
      { type: 'text', value: item.vessel },
      {
        type: 'text',
        value: (
          <span className="font-mono text-right">
            ${item.originalAmount.toLocaleString()}
          </span>
        )
      },
      {
        type: 'text',
        value: (
          <span className="font-mono font-semibold text-right text-orange-600">
            ${item.splitAmount.toLocaleString()}
          </span>
        )
      },
      {
        type: 'badges',
        badges: [{
          intent: item.allocation === 'SEACHEF' ? 'warning' : 'info',
          content: item.allocation
        }]
      },
    ],
  }));

  return (
   
  );
};

export default Distribution;