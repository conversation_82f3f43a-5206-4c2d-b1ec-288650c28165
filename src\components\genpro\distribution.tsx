'use client';

import React, { useState, useMemo } from 'react';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import { Download, DollarSign, TrendingUp } from 'lucide-react';

import { Button } from '@/components/ui/Button';
import { Tabs } from '@/components/ui/Tabs';
import V2DataGrid from '@/components/ui/V2DataGrid';
import { APIService } from '@/service/api.service';
import { setToastAlert } from '@/slices/metaDataSlice';

interface DistributionProps {
  onNext?: () => void;
  uploadedFiles?: any;
  onDistributionComplete?: (data: any) => void;
  pivotData: any;
}

const Distribution: React.FC<DistributionProps> = ({
  onNext,
  uploadedFiles,
  onDistributionComplete,
  pivotData
}) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const apiService = useMemo(() => new APIService(dispatch, router), [dispatch, router]);
  const [selectedTab, setSelectedTab] = useState(0);

  // Extract data from pivotData
  const distributionData = pivotData?.result?.database_records || {};
  const summaryData = distributionData.pivot_summaries || {};
  const splitsData = distributionData.pivot_splits || {};
  const distributionsData = distributionData.pivot_distributions || {};

  // SMC Headers for Summary
  const smcSummaryHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Total BF Amount', width: 150, isSortable: true, filterType: '' },
  ];

  // Vessel Headers for Summary
  const vesselSummaryHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Vessel Name', width: 150, isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Total BF Amount', width: 150, isSortable: true, filterType: '' },
  ];

  // SMC Headers for Splits
  const smcSplitsHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Entity Amount', width: 150, isSortable: true, filterType: '' },
    { name: 'Seachef Amount', width: 150, isSortable: true, filterType: '' },
  ];

  // Vessel Headers for Splits
  const vesselSplitsHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Vessel Name', width: 150, isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Entity Amount', width: 150, isSortable: true, filterType: '' },
    { name: 'Seachef Amount', width: 150, isSortable: true, filterType: '' },
  ];

  // SMC Headers for Distributions
  const smcDistributionHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Base BF Amount', width: 150, isSortable: true, filterType: '' },
    { name: 'Total BF Including 50%', width: 180, isSortable: true, filterType: '' },
    { name: 'BF Percentage', width: 150, isSortable: true, filterType: '' },
    { name: 'Final Distribution Amount', width: 200, isSortable: true, filterType: '' },
  ];

  // Vessel Headers for Distributions
  const vesselDistributionHeaders = [
    { name: 'Entity Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Vessel Name', width: 150, isSortable: true, filterType: '' },
    { name: 'SMC Name', width: 150, isSortable: true, filterType: '' },
    { name: 'Base BF Amount', width: 150, isSortable: true, filterType: '' },
    { name: 'Total BF Including 50%', width: 180, isSortable: true, filterType: '' },
    { name: 'BF Percentage', width: 150, isSortable: true, filterType: '' },
    { name: 'Final Distribution Amount', width: 200, isSortable: true, filterType: '' },
  ];

  // Map SMC Summary data
  const smcSummaryData = (summaryData.smc?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'SMC Name': item.smc_name,
    'Total BF Amount': item.total_bf_amount?.toFixed(2),
  }));

  // Map Vessel Summary data
  const vesselSummaryData = (summaryData.vessel?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'Vessel Name': item.vessel_name,
    'SMC Name': item.smc_name,
    'Total BF Amount': item.total_bf_amount?.toFixed(2),
  }));

  // Map SMC Splits data
  const smcSplitsDataMapped = (splitsData.smc?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'SMC Name': item.smc_name,
    'Entity Amount': item.entity_amount?.toFixed(2),
    'Seachef Amount': item.seachef_amount?.toFixed(2),
  }));

  // Map Vessel Splits data
  const vesselSplitsDataMapped = (splitsData.vessel?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'Vessel Name': item.vessel_name,
    'SMC Name': item.smc_name,
    'Entity Amount': item.entity_amount?.toFixed(2),
    'Seachef Amount': item.seachef_amount?.toFixed(2),
  }));

  // Map SMC Distribution data
  const smcDistributionData = (distributionsData.smc?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'SMC Name': item.smc_name,
    'Base BF Amount': item.base_bf_amount?.toFixed(2),
    'Total BF Including 50%': item.total_bf_including_50_percent?.toFixed(2),
    'BF Percentage': item.bf_percentage?.toFixed(4) + '%',
    'Final Distribution Amount': item.final_distribution_amount?.toFixed(2),
  }));

  // Map Vessel Distribution data
  const vesselDistributionData = (distributionsData.vessel?.data || []).map((item: any) => ({
    'Entity Name': item.entity_name,
    'Vessel Name': item.vessel_name,
    'SMC Name': item.smc_name,
    'Base BF Amount': item.base_bf_amount?.toFixed(2),
    'Total BF Including 50%': item.total_bf_including_50_percent?.toFixed(2),
    'BF Percentage': item.bf_percentage?.toFixed(4) + '%',
    'Final Distribution Amount': item.final_distribution_amount?.toFixed(2),
  }));

  // Summary cards data
  const totalBfToDistribute = pivotData?.result?.total_bf_to_distribute || 0;
  const smcRecords = pivotData?.result?.smc_summary_records || 0;
  const vesselRecords = pivotData?.result?.vessel_summary_records || 0;

  // Download comprehensive report
  const handleDownloadReport = () => {
    const reportUrl = pivotData?.result?.comprehensive_report_url;
    if (reportUrl) {
      window.open(reportUrl, '_blank');
    } else {
      dispatch(setToastAlert({ type: 'error', message: 'Report URL not available' }));
    }
  };

  // Tab data configuration
  const tabData = [
    {
      title: 'Summary',
      component: (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-800">SMC Summary</h3>
            <div className="h-[300px]">
              <V2DataGrid data={smcSummaryData} headerList={smcSummaryHeaders} />
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-800">Vessel Summary</h3>
            <div className="h-[300px]">
              <V2DataGrid data={vesselSummaryData} headerList={vesselSummaryHeaders} />
            </div>
          </div>
        </div>
      ),
      icon: '',
      iconPosition: 'FIRST' as const,
    },
    {
      title: 'Splits',
      component: (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-800">SMC Splits</h3>
            <div className="h-[300px]">
              <V2DataGrid data={smcSplitsDataMapped} headerList={smcSplitsHeaders} />
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-800">Vessel Splits</h3>
            <div className="h-[300px]">
              <V2DataGrid data={vesselSplitsDataMapped} headerList={vesselSplitsHeaders} />
            </div>
          </div>
        </div>
      ),
      icon: '',
      iconPosition: 'FIRST' as const,
    },
    {
      title: 'Distribution',
      component: (
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-800">SMC Distribution</h3>
            <div className="h-[300px]">
              <V2DataGrid data={smcDistributionData} headerList={smcDistributionHeaders} />
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-3 text-gray-800">Vessel Distribution</h3>
            <div className="h-[300px]">
              <V2DataGrid data={vesselDistributionData} headerList={vesselDistributionHeaders} />
            </div>
          </div>
        </div>
      ),
      icon: '',
      iconPosition: 'FIRST' as const,
    },
  ];

  return (
    <div className="bg-white">
      {/* Download Button */}
      <div className="flex justify-end mb-4">
        <Button
          onClick={handleDownloadReport}
          className="flex items-center space-x-2"
        >
          <Download className="w-4 h-4" />
          <span>Download Comprehensive Report</span>
        </Button>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg">
        <Tabs
          data={tabData}
          selectedIndex={selectedTab}
          onChange={setSelectedTab}
        />
      </div>
    </div>
  );
};

export default Distribution;